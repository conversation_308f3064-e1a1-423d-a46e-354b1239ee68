/**
  ******************************************************************************
  * @file    ad9854.h
  * <AUTHOR> 第三问 AD9854 DDS信号发生器
  * @version V1.0
  * @date    2024
  * @brief   AD9854 DDS模块驱动头文件 - 专为5MHz/0.5V输出优化
  ******************************************************************************
  * @attention
  * 
  * AD9854技术规格：
  * - 系统时钟：300MHz (20MHz外部晶振 × 15倍频)
  * - 频率分辨率：48位 (0.0018Hz精度)
  * - 频率范围：0Hz ~ 150MHz
  * - 幅度控制：12位 (4096级精度)
  * - 输出峰峰值：0~500mV (通过Shape参数控制)
  * 
  * 硬件连接 (STM32F4 -> AD9854):
  * PA6  -> RST    (复位信号)
  * PA4  -> UDCLK  (更新时钟)
  * PA5  -> WR     (写使能)
  * PA8  -> RD     (读使能)
  * PA2  -> OSK    (OSK控制)
  * PB10 -> FSK    (FSK/BPSK/HOLD控制)
  * PC0-7 -> D0-D7 (8位数据总线)
  * PC8-13 -> A0-A5 (6位地址总线)
  * 
  ******************************************************************************
  */

#ifndef __AD9854_H
#define __AD9854_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "stm32f4xx_gpio.h"
#include "stm32f4xx_rcc.h"
#include <stdint.h>
#include <math.h>

/* Exported types ------------------------------------------------------------*/
typedef enum {
    AD9854_OK = 0,
    AD9854_ERROR = 1,
    AD9854_TIMEOUT = 2,
    AD9854_PARAM_ERROR = 3
} AD9854_StatusTypeDef;

/**
 * @brief AD9854控制参数结构体
 */
typedef struct {
    double frequency_hz;        // 输出频率 (Hz)
    double target_vpp_mv;       // 目标峰峰值 (mV) - 考虑后续电路增益后的最终输出
    double ad9854_vpp_mv;       // AD9854实际输出峰峰值 (mV) - 补偿后的值
    uint16_t amplitude_code;    // AD9854幅度控制码 (0-4095)
    double gain_factor;         // 后续电路增益系数
    uint8_t enable;             // 输出使能标志
} AD9854_ControlParams_t;

/**
 * @brief 外部控制接口类型
 */
typedef enum {
    CONTROL_INTERFACE_NONE = 0,
    CONTROL_INTERFACE_UART_SCREEN = 1,  // 串口屏控制
    CONTROL_INTERFACE_MATRIX_KEYPAD = 2 // 4x4矩阵键盘控制
} AD9854_ControlInterface_t;

/**
 * @brief 控制命令类型
 */
typedef enum {
    CMD_SET_FREQUENCY = 0x01,    // 设置频率
    CMD_SET_AMPLITUDE = 0x02,    // 设置幅度
    CMD_SET_GAIN = 0x03,         // 设置增益系数
    CMD_ENABLE_OUTPUT = 0x04,    // 使能输出
    CMD_DISABLE_OUTPUT = 0x05,   // 禁用输出
    CMD_GET_STATUS = 0x06,       // 获取状态
    CMD_SAVE_PARAMS = 0x07,      // 保存参数
    CMD_LOAD_PARAMS = 0x08       // 加载参数
} AD9854_Command_t;

/* Exported constants --------------------------------------------------------*/
// AD9854系统时钟配置 (20MHz外部晶振 × 15倍频 = 300MHz)
#define AD9854_SYSCLK_HZ        300000000UL
#define AD9854_CLK_MULTIPLIER   15

// 频率和幅度限制
#define AD9854_MAX_FREQUENCY    150000000UL  // 150MHz最大频率
#define AD9854_MIN_FREQUENCY    1UL          // 1Hz最小频率
#define AD9854_MAX_AMPLITUDE    4095         // 12位幅度控制最大值
#define AD9854_MIN_AMPLITUDE    0            // 最小幅度

// AD9854硬件输出特性
#define AD9854_MAX_OUTPUT_VPP   500.0        // AD9854最大输出峰峰值 (mV)
#define AD9854_MIN_OUTPUT_VPP   0.0          // AD9854最小输出峰峰值 (mV)

// 电赛G题系统参数 (可通过外部接口调整)
#define DEFAULT_TARGET_FREQ     5000000.0    // 默认目标频率 (5MHz)
#define DEFAULT_TARGET_VPP      500.0        // 默认目标峰峰值 (500mV)
#define DEFAULT_GAIN_FACTOR     1.0          // 默认增益系数 (无增益)

// 控制参数范围限制
#define MAX_TARGET_VPP          5000.0       // 最大目标峰峰值 (5V)
#define MIN_TARGET_VPP          10.0         // 最小目标峰峰值 (10mV)
#define MAX_GAIN_FACTOR         100.0        // 最大增益系数
#define MIN_GAIN_FACTOR         0.01         // 最小增益系数

// GPIO引脚定义 (适配嘉立创天空星STM32F407)
// 基于天空星开发板的实际引脚资源分配
#define AD9854_RST_PORT         GPIOE
#define AD9854_RST_PIN          GPIO_Pin_4
#define AD9854_UDCLK_PORT       GPIOE
#define AD9854_UDCLK_PIN        GPIO_Pin_5
#define AD9854_WR_PORT          GPIOE
#define AD9854_WR_PIN           GPIO_Pin_6
#define AD9854_RD_PORT          GPIOB
#define AD9854_RD_PIN           GPIO_Pin_8
#define AD9854_OSK_PORT         GPIOB
#define AD9854_OSK_PIN          GPIO_Pin_9
#define AD9854_FSK_PORT         GPIOD
#define AD9854_FSK_PIN          GPIO_Pin_12

// 数据总线和地址总线 (使用GPIOD和GPIOE的可用引脚)
// 数据总线 D0-D7 使用GPIOD的PD0-PD7
#define AD9854_DATA_PORT        GPIOD
#define AD9854_DATA_PINS        (GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | \
                                GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6 | GPIO_Pin_7)

// 地址总线 A0-A5 使用GPIOE的PE8-PE13
#define AD9854_ADDR_PORT        GPIOE
#define AD9854_ADDR_PINS        (GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_10 | GPIO_Pin_11 | \
                                GPIO_Pin_12 | GPIO_Pin_13)

// 控制信号宏定义
#define AD9854_RST_HIGH()       GPIO_SetBits(AD9854_RST_PORT, AD9854_RST_PIN)
#define AD9854_RST_LOW()        GPIO_ResetBits(AD9854_RST_PORT, AD9854_RST_PIN)
#define AD9854_UDCLK_HIGH()     GPIO_SetBits(AD9854_UDCLK_PORT, AD9854_UDCLK_PIN)
#define AD9854_UDCLK_LOW()      GPIO_ResetBits(AD9854_UDCLK_PORT, AD9854_UDCLK_PIN)
#define AD9854_WR_HIGH()        GPIO_SetBits(AD9854_WR_PORT, AD9854_WR_PIN)
#define AD9854_WR_LOW()         GPIO_ResetBits(AD9854_WR_PORT, AD9854_WR_PIN)
#define AD9854_RD_HIGH()        GPIO_SetBits(GPIOB, AD9854_RD_PIN)
#define AD9854_RD_LOW()         GPIO_ResetBits(GPIOB, AD9854_RD_PIN)
#define AD9854_OSK_HIGH()       GPIO_SetBits(GPIOB, AD9854_OSK_PIN)
#define AD9854_OSK_LOW()        GPIO_ResetBits(GPIOB, AD9854_OSK_PIN)

// AD9854寄存器地址
#define AD9854_REG_PHASE1       0x00    // 相位寄存器1
#define AD9854_REG_PHASE2       0x02    // 相位寄存器2
#define AD9854_REG_FREQ1        0x04    // 频率寄存器1
#define AD9854_REG_FREQ2        0x0A    // 频率寄存器2
#define AD9854_REG_DELTA_FREQ   0x10    // 增量频率寄存器
#define AD9854_REG_UPDATE_CLK   0x16    // 更新时钟寄存器
#define AD9854_REG_RAMP_RATE    0x1A    // 斜率时钟寄存器
#define AD9854_REG_CONTROL      0x1D    // 控制寄存器
#define AD9854_REG_CLK_MULT     0x1E    // 时钟倍频寄存器
#define AD9854_REG_MODE         0x1F    // 模式寄存器
#define AD9854_REG_QDAC         0x20    // QDAC寄存器
#define AD9854_REG_I_MULT       0x21    // I通道倍增器
#define AD9854_REG_Q_MULT       0x23    // Q通道倍增器
#define AD9854_REG_OSK_MULT     0x25    // OSK倍增器

/* Exported macro ------------------------------------------------------------*/
// 频率转换宏 (48位频率字计算)
#define AD9854_FREQ_TO_WORD(freq) ((uint64_t)((double)(freq) * 281474976710656.0 / (double)AD9854_SYSCLK_HZ))

// 幅度转换宏 (12位幅度控制)
#define AD9854_VOLTAGE_TO_AMPLITUDE(voltage_mv) ((uint16_t)((voltage_mv) * 4095 / 500))

/* Exported functions --------------------------------------------------------*/

// ==================== 基础硬件控制函数 ====================

/**
 * @brief  AD9854系统初始化
 * @param  None
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_Init(void);

/**
 * @brief  AD9854 GPIO初始化
 * @param  None
 * @retval None
 */
void AD9854_GPIO_Init(void);

/**
 * @brief  AD9854写寄存器
 * @param  addr: 寄存器地址
 * @param  data: 写入数据
 * @retval None
 */
void AD9854_WriteReg(uint8_t addr, uint8_t data);

/**
 * @brief  AD9854更新输出
 * @param  None
 * @retval None
 */
void AD9854_Update(void);

/**
 * @brief  AD9854复位
 * @param  None
 * @retval None
 */
void AD9854_Reset(void);

// ==================== 参数控制函数 ====================

/**
 * @brief  设置目标频率 (考虑外部控制)
 * @param  frequency_hz: 目标频率 (Hz)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetTargetFrequency(double frequency_hz);

/**
 * @brief  设置目标峰峰值 (考虑后续电路增益)
 * @param  target_vpp_mv: 目标峰峰值 (mV)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetTargetAmplitude(double target_vpp_mv);

/**
 * @brief  设置后续电路增益系数
 * @param  gain_factor: 增益系数 (后续电路的电压增益)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetGainFactor(double gain_factor);

/**
 * @brief  使能/禁用输出
 * @param  enable: 1-使能输出, 0-禁用输出
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_EnableOutput(uint8_t enable);

// ==================== 外部控制接口函数 ====================

/**
 * @brief  初始化外部控制接口
 * @param  interface_type: 控制接口类型
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_InitControlInterface(AD9854_ControlInterface_t interface_type);

/**
 * @brief  处理外部控制命令
 * @param  command: 控制命令
 * @param  param1: 参数1
 * @param  param2: 参数2
 * @retval AD9854_StatusTypeDef 处理状态
 */
AD9854_StatusTypeDef AD9854_ProcessCommand(AD9854_Command_t command, double param1, double param2);

/**
 * @brief  获取当前控制参数
 * @param  params: 参数结构体指针
 * @retval AD9854_StatusTypeDef 获取状态
 */
AD9854_StatusTypeDef AD9854_GetControlParams(AD9854_ControlParams_t *params);

// ==================== 增益补偿算法函数 ====================

/**
 * @brief  计算AD9854所需输出幅度 (增益补偿)
 * @param  target_vpp_mv: 目标峰峰值 (mV)
 * @param  gain_factor: 后续电路增益系数
 * @retval double AD9854所需输出峰峰值 (mV)
 */
double AD9854_CalculateRequiredOutput(double target_vpp_mv, double gain_factor);

/**
 * @brief  峰峰值转换为幅度控制码
 * @param  vpp_mv: 峰峰值 (mV)
 * @retval uint16_t 幅度控制码 (0-4095)
 */
uint16_t AD9854_VppToAmplitudeCode(double vpp_mv);

/**
 * @brief  幅度控制码转换为峰峰值
 * @param  amplitude_code: 幅度控制码 (0-4095)
 * @retval double 峰峰值 (mV)
 */
double AD9854_AmplitudeCodeToVpp(uint16_t amplitude_code);

// ==================== 工具函数 ====================

/**
 * @brief  延时函数 (微秒级)
 * @param  us: 延时时间 (微秒)
 * @retval None
 */
void AD9854_Delay_us(uint32_t us);

/**
 * @brief  参数有效性检查
 * @param  params: 参数结构体指针
 * @retval AD9854_StatusTypeDef 检查结果
 */
AD9854_StatusTypeDef AD9854_ValidateParams(const AD9854_ControlParams_t *params);

#ifdef __cplusplus
}
#endif

#endif /* __AD9854_H */

/**
  * @}
  */

/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

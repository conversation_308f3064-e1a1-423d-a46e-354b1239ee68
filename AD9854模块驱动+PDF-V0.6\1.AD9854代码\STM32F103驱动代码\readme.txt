﻿/*********************************************************************************************/
【*】简介

-工程名称：AD9854-STM32-DDS驱动板-Vx.x
-实验平台: 康威DDS驱动板
-MDK版本：KEIL 5.14.0
-ST固件库版本：3.5.0
	工程文件位于对应文件夹的"\USER"目录下，用keil5软件打开VirtualCOMPort.uvprojx即可

程序0-7均可直接配合本店DDS驱动板直接使用

【 ！】实验操作：
1、使用排线连接驱动板与AD9854模块，驱动板上排线方向唯一，驱动板以及DDS模块上，板子背面双排针，方形焊盘为双排针1脚，对应连接即可
2、使用5V电源连接AD9854模块，使用5V电源连接DDS驱动板，
3、AD9854模块侧面输出高频头，使用SMA屏蔽线，连接示波器
3、编译该代码，使用jlink下载器下载代码到驱动板
4、确保AD9854模块已上电。按下驱动板复位键(或驱动板重新上电)，调整示波器时间档位到合适位置,即可观察到输出波形

5、如使用自己的STM32开发板，请按下方描述接线，如使用其他引脚请自行移植更改程序。
/*********************************************************************************************/

【*】 引脚分配
	
STM32控制板		模块丝印	芯片引脚名	功能
PA6		----->	RST		MASTER RESET	初始化串行/并行编程总线，寄存器恢复默认值。数字输入。置高(3.3V)芯片复位
PA4		<----->	UCLK		I/O UD CLK	双向I/O更新时钟。在控制寄存器中选择方向。
PA5		----->	WD		WR/SCLK		将并行数据写入I/O端口缓冲区。与SCLK共享功能。
PA8		<---->	RD		RD/CS		从编程寄存器读取并行数据。与CS共享功能。
PA2		----->	OSK		OSK 		输出OSK控制	
PB10		----->	FSK		FSK/BPSK/HOLD   FSK、BPSK、线性调频模式的控制脚
PC0-7		----->	D0-D7		D0-D7		8位双向并行编程数据输入。仅用于并行编程模式。 
PC8		----->	A0		A0/SDIO		程序寄存器的并行地址输入/SDIO用于2线串行通信模式。
PC9		----->	A1		A1/SDO	 	程序寄存器的并行地址输入/SDO用于3线串行通信模式。
PC10		----->	A2		A2/IO RESET	程序寄存器的并行地址输入/IO RESET用于
选择串行编程模式
PC11-13		----->	A3-A5		A3-A5		程序寄存器的并行地址输入

3.3V		----->	3V3			控制板3.3V与AD9854模块3.3V连接。
GND		<---->	GND			控制板与AD9854模块需共地。
浮空		------	其他			所有未说明但模块有留出管脚，未使用可直接浮空，功能请参考数据手册		

/*********************************************************************************************/

【*】 版本

-程序版本：0.6
-更新日期：2023-05-xx



/*********************************************************************************************/

【*】 联系我们

-淘宝店铺    https://kvdz.taobao.com

/*********************************************************************************************/
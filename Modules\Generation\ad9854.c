/**
  ******************************************************************************
  * @file    ad9854.c
  * <AUTHOR> 第三问 AD9854 DDS信号发生器
  * @version V1.0
  * @date    2024
  * @brief   AD9854 DDS模块驱动实现 - 专为5MHz/0.5V输出优化
  ******************************************************************************
  * @attention
  * 
  * 本驱动专门针对第三问需求优化：
  * - 输出频率：5MHz正弦波
  * - 输出幅度：0.5V峰峰值
  * - 波形质量：高精度、低失真
  * - 系统资源：最小化CPU占用
  * 
  * AD9854优势：
  * - 48位频率分辨率，精度达0.0018Hz
  * - 12位幅度控制，4096级精度
  * - 300MHz系统时钟，支持高频输出
  * - 硬件DDS，CPU占用率接近0
  * 
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9854.h"
#include <stdint.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
// 频率计算常数 (优化计算性能)
#define FREQ_CALC_CONSTANT      281474976710656.0  // 2^48
#define SYSCLK_DOUBLE           300000000.0        // 系统时钟 (double)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint8_t freq_word[6];  // 48位频率字缓存

/* Private function prototypes -----------------------------------------------*/
static void AD9854_FrequencyToWord(double frequency);
static void AD9854_WriteFrequencyWord(uint8_t reg_addr);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  AD9854初始化
 * @param  None
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_Init(void)
{
    // GPIO初始化
    AD9854_GPIO_Init();
    
    // 延时等待硬件稳定
    AD9854_Delay_us(1000);
    
    // 复位AD9854
    AD9854_Reset();
    
    // 配置AD9854寄存器
    AD9854_WR_HIGH();  // 写信号默认高电平
    AD9854_RD_HIGH();  // 读信号默认高电平
    AD9854_UDCLK_LOW(); // 更新时钟默认低电平
    
    // 配置控制寄存器 (关闭比较器)
    AD9854_WriteReg(AD9854_REG_CONTROL, 0x00);
    
    // 配置时钟倍频寄存器 (15倍频: 20MHz -> 300MHz)
    AD9854_WriteReg(AD9854_REG_CLK_MULT, AD9854_CLK_MULTIPLIER);
    
    // 配置模式寄存器 (单频模式，外部更新)
    AD9854_WriteReg(AD9854_REG_MODE, 0x00);
    
    // 配置QDAC寄存器 (可编程电流输出，取消旁路)
    AD9854_WriteReg(AD9854_REG_QDAC, 0x60);
    
    // 更新配置
    AD9854_Update();
    
    return AD9854_OK;
}

/**
 * @brief  AD9854 GPIO初始化
 * @param  None
 * @retval None
 */
void AD9854_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能GPIO时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOB | 
                          RCC_AHB1Periph_GPIOC, ENABLE);
    
    // 配置控制信号引脚 (PA2, PA4, PA5, PA6, PA8)
    GPIO_InitStructure.GPIO_Pin = AD9854_RST_PIN | AD9854_UDCLK_PIN | 
                                 AD9854_WR_PIN | AD9854_RD_PIN | AD9854_OSK_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // 配置FSK控制引脚 (PB10)
    GPIO_InitStructure.GPIO_Pin = AD9854_FSK_PIN;
    GPIO_Init(GPIOB, &GPIO_InitStructure);
    
    // 配置数据总线和地址总线 (PC0-PC13)
    GPIO_InitStructure.GPIO_Pin = AD9854_DATA_PINS | AD9854_ADDR_PINS;
    GPIO_Init(GPIOC, &GPIO_InitStructure);
    
    // 设置初始状态
    AD9854_RST_LOW();
    AD9854_WR_HIGH();
    AD9854_RD_HIGH();
    AD9854_UDCLK_LOW();
    AD9854_OSK_LOW();
    GPIO_ResetBits(GPIOB, AD9854_FSK_PIN);
}

/**
 * @brief  AD9854写寄存器
 * @param  addr: 寄存器地址
 * @param  data: 写入数据
 * @retval None
 */
void AD9854_WriteReg(uint8_t addr, uint8_t data)
{
    uint16_t addr_data;
    uint16_t pin_mask;
    uint8_t i;

    // 组合地址和数据 (地址在高8位，数据在低8位)
    addr_data = ((uint16_t)addr << 8) | data;

    // 设置地址和数据总线 (PC0-PC13)
    pin_mask = 0x3FFF;  // PC0-PC13对应的位掩码

    // 先复位所有相关引脚
    GPIO_ResetBits(GPIOC, pin_mask);

    // 然后设置需要为1的引脚
    for (i = 0; i < 14; i++) {
        if (addr_data & (1 << i)) {
            GPIO_SetBits(GPIOC, 1 << i);
        }
    }

    // 写时序：WR低电平有效
    AD9854_WR_LOW();
    AD9854_Delay_us(1);  // 写脉冲宽度
    AD9854_WR_HIGH();
    AD9854_Delay_us(1);  // 恢复时间
}

/**
 * @brief  AD9854设置频率 (高精度double版本)
 * @param  frequency: 频率值 (Hz)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetFrequency(double frequency)
{
    // 频率范围检查
    if (frequency < AD9854_MIN_FREQUENCY || frequency > AD9854_MAX_FREQUENCY) {
        return AD9854_ERROR;
    }
    
    // 转换频率为48位频率字
    AD9854_FrequencyToWord(frequency);
    
    // 写入频率寄存器1
    AD9854_WriteFrequencyWord(AD9854_REG_FREQ1);
    
    // 更新输出
    AD9854_Update();
    
    return AD9854_OK;
}

/**
 * @brief  AD9854设置幅度
 * @param  amplitude: 幅度值 (0-4095)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetAmplitude(uint16_t amplitude)
{
    // 幅度范围检查
    if (amplitude > AD9854_MAX_AMPLITUDE) {
        return AD9854_ERROR;
    }
    
    // 设置I通道幅度
    AD9854_WriteReg(AD9854_REG_I_MULT, (uint8_t)(amplitude >> 8));
    AD9854_WriteReg(AD9854_REG_I_MULT + 1, (uint8_t)(amplitude & 0xFF));
    
    // 设置Q通道幅度 (与I通道相同，确保正弦波输出)
    AD9854_WriteReg(AD9854_REG_Q_MULT, (uint8_t)(amplitude >> 8));
    AD9854_WriteReg(AD9854_REG_Q_MULT + 1, (uint8_t)(amplitude & 0xFF));
    
    // 更新输出
    AD9854_Update();
    
    return AD9854_OK;
}

/**
 * @brief  AD9854设置正弦波输出 (频率+幅度)
 * @param  frequency: 频率值 (Hz)
 * @param  amplitude: 幅度值 (0-4095)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetSineWave(double frequency, uint16_t amplitude)
{
    AD9854_StatusTypeDef status;
    
    // 设置频率
    status = AD9854_SetFrequency(frequency);
    if (status != AD9854_OK) {
        return status;
    }
    
    // 设置幅度
    status = AD9854_SetAmplitude(amplitude);
    if (status != AD9854_OK) {
        return status;
    }
    
    return AD9854_OK;
}

/**
 * @brief  AD9854配置为5MHz/0.5V输出 (预设配置)
 * @param  None
 * @retval AD9854_StatusTypeDef 配置状态
 */
AD9854_StatusTypeDef AD9854_Config_5MHz_500mV(void)
{
    return AD9854_SetSineWave(AD9854_TARGET_FREQ, AD9854_TARGET_AMPLITUDE);
}

/**
 * @brief  AD9854更新输出
 * @param  None
 * @retval None
 */
void AD9854_Update(void)
{
    // 产生更新时钟脉冲
    AD9854_UDCLK_HIGH();
    AD9854_Delay_us(1);
    AD9854_UDCLK_LOW();
    AD9854_Delay_us(1);
}

/**
 * @brief  AD9854复位
 * @param  None
 * @retval None
 */
void AD9854_Reset(void)
{
    // 复位脉冲：高电平有效
    AD9854_RST_HIGH();
    AD9854_Delay_us(100);  // 复位脉冲宽度
    AD9854_RST_LOW();
    AD9854_Delay_us(100);  // 复位后稳定时间
}

/**
 * @brief  延时函数 (微秒级)
 * @param  us: 延时时间 (微秒)
 * @retval None
 */
void AD9854_Delay_us(uint32_t us)
{
    // 基于168MHz系统时钟的精确延时
    // 每个循环约6个时钟周期，168MHz下约35.7ns
    uint32_t cycles = us * 28;  // 约1微秒
    
    while (cycles--) {
        __NOP();  // 空操作指令
    }
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  频率转换为48位频率字
 * @param  frequency: 频率值 (Hz)
 * @retval None
 */
static void AD9854_FrequencyToWord(double frequency)
{
    uint64_t freq_word_64;
    uint32_t freq_low, freq_high;
    
    // 计算48位频率字: FTW = (frequency * 2^48) / SYSCLK
    freq_word_64 = (uint64_t)(frequency * FREQ_CALC_CONSTANT / SYSCLK_DOUBLE);
    
    // 分离高32位和低32位
    freq_low = (uint32_t)(freq_word_64 & 0xFFFFFFFF);
    freq_high = (uint32_t)(freq_word_64 >> 32);
    
    // 转换为6字节数组 (小端序)
    freq_word[0] = (uint8_t)(freq_low & 0xFF);
    freq_word[1] = (uint8_t)((freq_low >> 8) & 0xFF);
    freq_word[2] = (uint8_t)((freq_low >> 16) & 0xFF);
    freq_word[3] = (uint8_t)((freq_low >> 24) & 0xFF);
    freq_word[4] = (uint8_t)(freq_high & 0xFF);
    freq_word[5] = (uint8_t)((freq_high >> 8) & 0xFF);
}

/**
 * @brief  写入48位频率字到寄存器
 * @param  reg_addr: 频率寄存器起始地址
 * @retval None
 */
static void AD9854_WriteFrequencyWord(uint8_t reg_addr)
{
    uint8_t i;
    
    // 写入6字节频率字 (从高字节到低字节)
    for (i = 0; i < 6; i++) {
        AD9854_WriteReg(reg_addr + i, freq_word[5 - i]);
    }
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

/**
  ******************************************************************************
  * @file    ad9854.c
  * <AUTHOR> 第三问 AD9854 DDS信号发生器
  * @version V1.0
  * @date    2024
  * @brief   AD9854 DDS模块驱动实现 - 专为5MHz/0.5V输出优化
  ******************************************************************************
  * @attention
  * 
  * 本驱动专门针对第三问需求优化：
  * - 输出频率：5MHz正弦波
  * - 输出幅度：0.5V峰峰值
  * - 波形质量：高精度、低失真
  * - 系统资源：最小化CPU占用
  * 
  * AD9854优势：
  * - 48位频率分辨率，精度达0.0018Hz
  * - 12位幅度控制，4096级精度
  * - 300MHz系统时钟，支持高频输出
  * - 硬件DDS，CPU占用率接近0
  * 
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ad9854.h"
#include <stdint.h>
#include <stddef.h>  // 包含NULL定义

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
// 频率计算常数 (优化计算性能)
#define FREQ_CALC_CONSTANT      281474976710656.0  // 2^48
#define SYSCLK_DOUBLE           300000000.0        // 系统时钟 (double)

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static uint8_t freq_word[6];  // 48位频率字缓存

// 全局控制参数
static AD9854_ControlParams_t g_control_params = {
    .frequency_hz = DEFAULT_TARGET_FREQ,
    .target_vpp_mv = DEFAULT_TARGET_VPP,
    .ad9854_vpp_mv = DEFAULT_TARGET_VPP,
    .amplitude_code = 4095,
    .gain_factor = DEFAULT_GAIN_FACTOR,
    .enable = 1
};

// 控制接口类型 (预留，后续实现时使用)
// static AD9854_ControlInterface_t g_control_interface = CONTROL_INTERFACE_NONE;

/* Private function prototypes -----------------------------------------------*/
static void AD9854_FrequencyToWord(double frequency);
static void AD9854_WriteFrequencyWord(uint8_t reg_addr);
static AD9854_StatusTypeDef AD9854_SetFrequency(double frequency);
static AD9854_StatusTypeDef AD9854_SetAmplitude(uint16_t amplitude);
static AD9854_StatusTypeDef AD9854_ApplyCurrentParams(void);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief  AD9854初始化
 * @param  None
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_Init(void)
{
    // GPIO初始化
    AD9854_GPIO_Init();
    
    // 延时等待硬件稳定
    AD9854_Delay_us(1000);
    
    // 复位AD9854
    AD9854_Reset();
    
    // 配置AD9854寄存器
    AD9854_WR_HIGH();  // 写信号默认高电平
    AD9854_RD_HIGH();  // 读信号默认高电平
    AD9854_UDCLK_LOW(); // 更新时钟默认低电平
    
    // 配置控制寄存器 (关闭比较器)
    AD9854_WriteReg(AD9854_REG_CONTROL, 0x00);
    
    // 配置时钟倍频寄存器 (15倍频: 20MHz -> 300MHz)
    AD9854_WriteReg(AD9854_REG_CLK_MULT, AD9854_CLK_MULTIPLIER);
    
    // 配置模式寄存器 (单频模式，外部更新)
    AD9854_WriteReg(AD9854_REG_MODE, 0x00);
    
    // 配置QDAC寄存器 (可编程电流输出，取消旁路)
    AD9854_WriteReg(AD9854_REG_QDAC, 0x60);
    
    // 更新配置
    AD9854_Update();
    
    return AD9854_OK;
}

/**
 * @brief  AD9854 GPIO初始化
 * @param  None
 * @retval None
 */
void AD9854_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;

    // 使能GPIO时钟 (GPIOB, GPIOD, GPIOE)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB | RCC_AHB1Periph_GPIOD |
                          RCC_AHB1Periph_GPIOE, ENABLE);

    // 配置GPIOE控制信号引脚 (PE4, PE5, PE6)
    GPIO_InitStructure.GPIO_Pin = AD9854_RST_PIN | AD9854_UDCLK_PIN | AD9854_WR_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(AD9854_RST_PORT, &GPIO_InitStructure);

    // 配置GPIOB控制信号引脚 (PB8, PB9)
    GPIO_InitStructure.GPIO_Pin = AD9854_RD_PIN | AD9854_OSK_PIN;
    GPIO_Init(GPIOB, &GPIO_InitStructure);

    // 配置GPIOD FSK控制引脚 (PD12)
    GPIO_InitStructure.GPIO_Pin = AD9854_FSK_PIN;
    GPIO_Init(GPIOD, &GPIO_InitStructure);

    // 配置数据总线 (GPIOD: PD0-PD7)
    GPIO_InitStructure.GPIO_Pin = AD9854_DATA_PINS;
    GPIO_Init(AD9854_DATA_PORT, &GPIO_InitStructure);

    // 配置地址总线 (GPIOE: PE8-PE13)
    GPIO_InitStructure.GPIO_Pin = AD9854_ADDR_PINS;
    GPIO_Init(AD9854_ADDR_PORT, &GPIO_InitStructure);

    // 设置初始状态
    AD9854_RST_LOW();
    AD9854_WR_HIGH();
    AD9854_RD_HIGH();
    AD9854_UDCLK_LOW();
    AD9854_OSK_LOW();
    GPIO_ResetBits(GPIOD, AD9854_FSK_PIN);
}

/**
 * @brief  AD9854写寄存器
 * @param  addr: 寄存器地址
 * @param  data: 写入数据
 * @retval None
 */
void AD9854_WriteReg(uint8_t addr, uint8_t data)
{
    uint8_t i;

    // 设置数据总线 (GPIOD: PD0-PD7)
    GPIO_ResetBits(AD9854_DATA_PORT, AD9854_DATA_PINS);
    for (i = 0; i < 8; i++) {
        if (data & (1 << i)) {
            GPIO_SetBits(AD9854_DATA_PORT, 1 << i);
        }
    }

    // 设置地址总线 (GPIOE: PE8-PE13, 只使用低6位地址)
    GPIO_ResetBits(AD9854_ADDR_PORT, AD9854_ADDR_PINS);
    for (i = 0; i < 6; i++) {
        if (addr & (1 << i)) {
            GPIO_SetBits(AD9854_ADDR_PORT, GPIO_Pin_8 << i);
        }
    }

    // 写时序：WR低电平有效
    AD9854_WR_LOW();
    AD9854_Delay_us(1);  // 写脉冲宽度
    AD9854_WR_HIGH();
    AD9854_Delay_us(1);  // 恢复时间
}

/**
 * @brief  AD9854设置频率 (内部函数)
 * @param  frequency: 频率值 (Hz)
 * @retval AD9854_StatusTypeDef 设置状态
 */
static AD9854_StatusTypeDef AD9854_SetFrequency(double frequency)
{
    // 频率范围检查
    if (frequency < AD9854_MIN_FREQUENCY || frequency > AD9854_MAX_FREQUENCY) {
        return AD9854_ERROR;
    }

    // 转换频率为48位频率字
    AD9854_FrequencyToWord(frequency);

    // 写入频率寄存器1
    AD9854_WriteFrequencyWord(AD9854_REG_FREQ1);

    // 更新输出
    AD9854_Update();

    return AD9854_OK;
}

/**
 * @brief  AD9854设置幅度 (内部函数)
 * @param  amplitude: 幅度值 (0-4095)
 * @retval AD9854_StatusTypeDef 设置状态
 */
static AD9854_StatusTypeDef AD9854_SetAmplitude(uint16_t amplitude)
{
    // 幅度范围检查
    if (amplitude > AD9854_MAX_AMPLITUDE) {
        return AD9854_ERROR;
    }

    // 设置I通道幅度
    AD9854_WriteReg(AD9854_REG_I_MULT, (uint8_t)(amplitude >> 8));
    AD9854_WriteReg(AD9854_REG_I_MULT + 1, (uint8_t)(amplitude & 0xFF));

    // 设置Q通道幅度 (与I通道相同，确保正弦波输出)
    AD9854_WriteReg(AD9854_REG_Q_MULT, (uint8_t)(amplitude >> 8));
    AD9854_WriteReg(AD9854_REG_Q_MULT + 1, (uint8_t)(amplitude & 0xFF));

    // 更新输出
    AD9854_Update();

    return AD9854_OK;
}

// ==================== 外部控制接口函数实现 ====================

/**
 * @brief  设置目标频率 (考虑外部控制)
 * @param  frequency_hz: 目标频率 (Hz)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetTargetFrequency(double frequency_hz)
{
    // 参数有效性检查
    if (frequency_hz < AD9854_MIN_FREQUENCY || frequency_hz > AD9854_MAX_FREQUENCY) {
        return AD9854_PARAM_ERROR;
    }

    // 更新控制参数
    g_control_params.frequency_hz = frequency_hz;

    // 应用参数到硬件
    return AD9854_ApplyCurrentParams();
}

/**
 * @brief  设置目标峰峰值 (考虑后续电路增益)
 * @param  target_vpp_mv: 目标峰峰值 (mV)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetTargetAmplitude(double target_vpp_mv)
{
    // 参数有效性检查
    if (target_vpp_mv < MIN_TARGET_VPP || target_vpp_mv > MAX_TARGET_VPP) {
        return AD9854_PARAM_ERROR;
    }

    // 更新控制参数
    g_control_params.target_vpp_mv = target_vpp_mv;

    // 计算AD9854所需输出 (考虑增益补偿)
    g_control_params.ad9854_vpp_mv = AD9854_CalculateRequiredOutput(
        target_vpp_mv, g_control_params.gain_factor);

    // 转换为幅度控制码
    g_control_params.amplitude_code = AD9854_VppToAmplitudeCode(
        g_control_params.ad9854_vpp_mv);

    // 应用参数到硬件
    return AD9854_ApplyCurrentParams();
}

/**
 * @brief  设置后续电路增益系数
 * @param  gain_factor: 增益系数 (后续电路的电压增益)
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_SetGainFactor(double gain_factor)
{
    // 参数有效性检查
    if (gain_factor < MIN_GAIN_FACTOR || gain_factor > MAX_GAIN_FACTOR) {
        return AD9854_PARAM_ERROR;
    }

    // 更新增益系数
    g_control_params.gain_factor = gain_factor;

    // 重新计算AD9854所需输出
    g_control_params.ad9854_vpp_mv = AD9854_CalculateRequiredOutput(
        g_control_params.target_vpp_mv, gain_factor);

    // 转换为幅度控制码
    g_control_params.amplitude_code = AD9854_VppToAmplitudeCode(
        g_control_params.ad9854_vpp_mv);

    // 应用参数到硬件
    return AD9854_ApplyCurrentParams();
}

/**
 * @brief  使能/禁用输出
 * @param  enable: 1-使能输出, 0-禁用输出
 * @retval AD9854_StatusTypeDef 设置状态
 */
AD9854_StatusTypeDef AD9854_EnableOutput(uint8_t enable)
{
    g_control_params.enable = enable;

    if (enable) {
        // 使能输出：应用当前参数
        return AD9854_ApplyCurrentParams();
    } else {
        // 禁用输出：设置幅度为0
        AD9854_SetAmplitude(0);
        return AD9854_OK;
    }
}

/**
 * @brief  初始化外部控制接口
 * @param  interface_type: 控制接口类型
 * @retval AD9854_StatusTypeDef 初始化状态
 */
AD9854_StatusTypeDef AD9854_InitControlInterface(AD9854_ControlInterface_t interface_type)
{
    // g_control_interface = interface_type;  // 预留，后续实现时启用

    switch (interface_type) {
        case CONTROL_INTERFACE_UART_SCREEN:
            // TODO: 初始化串口屏通信
            // 这里预留接口，后续实现
            break;

        case CONTROL_INTERFACE_MATRIX_KEYPAD:
            // TODO: 初始化4x4矩阵键盘
            // 这里预留接口，后续实现
            break;

        case CONTROL_INTERFACE_NONE:
        default:
            // 无外部控制接口
            break;
    }

    // 避免未使用参数警告
    (void)interface_type;

    return AD9854_OK;
}

/**
 * @brief  处理外部控制命令
 * @param  command: 控制命令
 * @param  param1: 参数1
 * @param  param2: 参数2
 * @retval AD9854_StatusTypeDef 处理状态
 */
AD9854_StatusTypeDef AD9854_ProcessCommand(AD9854_Command_t command, double param1, double param2)
{
    AD9854_StatusTypeDef status = AD9854_OK;

    switch (command) {
        case CMD_SET_FREQUENCY:
            status = AD9854_SetTargetFrequency(param1);
            break;

        case CMD_SET_AMPLITUDE:
            status = AD9854_SetTargetAmplitude(param1);
            break;

        case CMD_SET_GAIN:
            status = AD9854_SetGainFactor(param1);
            break;

        case CMD_ENABLE_OUTPUT:
            status = AD9854_EnableOutput(1);
            break;

        case CMD_DISABLE_OUTPUT:
            status = AD9854_EnableOutput(0);
            break;

        case CMD_GET_STATUS:
            // TODO: 返回状态信息
            break;

        case CMD_SAVE_PARAMS:
            // TODO: 保存参数到Flash
            break;

        case CMD_LOAD_PARAMS:
            // TODO: 从Flash加载参数
            break;

        default:
            status = AD9854_PARAM_ERROR;
            break;
    }

    return status;
}

/**
 * @brief  获取当前控制参数
 * @param  params: 参数结构体指针
 * @retval AD9854_StatusTypeDef 获取状态
 */
AD9854_StatusTypeDef AD9854_GetControlParams(AD9854_ControlParams_t *params)
{
    if (params == NULL) {
        return AD9854_PARAM_ERROR;
    }

    *params = g_control_params;
    return AD9854_OK;
}

/**
 * @brief  AD9854更新输出
 * @param  None
 * @retval None
 */
void AD9854_Update(void)
{
    // 产生更新时钟脉冲
    AD9854_UDCLK_HIGH();
    AD9854_Delay_us(1);
    AD9854_UDCLK_LOW();
    AD9854_Delay_us(1);
}

/**
 * @brief  AD9854复位
 * @param  None
 * @retval None
 */
void AD9854_Reset(void)
{
    // 复位脉冲：高电平有效
    AD9854_RST_HIGH();
    AD9854_Delay_us(100);  // 复位脉冲宽度
    AD9854_RST_LOW();
    AD9854_Delay_us(100);  // 复位后稳定时间
}

// ==================== 增益补偿算法函数实现 ====================

/**
 * @brief  计算AD9854所需输出幅度 (增益补偿)
 * @param  target_vpp_mv: 目标峰峰值 (mV)
 * @param  gain_factor: 后续电路增益系数
 * @retval double AD9854所需输出峰峰值 (mV)
 */
double AD9854_CalculateRequiredOutput(double target_vpp_mv, double gain_factor)
{
    double required_vpp;

    // 增益补偿计算：AD9854输出 = 目标输出 / 增益系数
    required_vpp = target_vpp_mv / gain_factor;

    // 限制在AD9854的输出范围内
    if (required_vpp > AD9854_MAX_OUTPUT_VPP) {
        required_vpp = AD9854_MAX_OUTPUT_VPP;
    } else if (required_vpp < AD9854_MIN_OUTPUT_VPP) {
        required_vpp = AD9854_MIN_OUTPUT_VPP;
    }

    return required_vpp;
}

/**
 * @brief  峰峰值转换为幅度控制码
 * @param  vpp_mv: 峰峰值 (mV)
 * @retval uint16_t 幅度控制码 (0-4095)
 */
uint16_t AD9854_VppToAmplitudeCode(double vpp_mv)
{
    uint16_t amplitude_code;

    // 线性转换：幅度码 = (峰峰值 / 最大峰峰值) * 最大幅度码
    amplitude_code = (uint16_t)((vpp_mv / AD9854_MAX_OUTPUT_VPP) * AD9854_MAX_AMPLITUDE);

    // 限制范围
    if (amplitude_code > AD9854_MAX_AMPLITUDE) {
        amplitude_code = AD9854_MAX_AMPLITUDE;
    }

    return amplitude_code;
}

/**
 * @brief  幅度控制码转换为峰峰值
 * @param  amplitude_code: 幅度控制码 (0-4095)
 * @retval double 峰峰值 (mV)
 */
double AD9854_AmplitudeCodeToVpp(uint16_t amplitude_code)
{
    // 线性转换：峰峰值 = (幅度码 / 最大幅度码) * 最大峰峰值
    return ((double)amplitude_code / AD9854_MAX_AMPLITUDE) * AD9854_MAX_OUTPUT_VPP;
}

/**
 * @brief  参数有效性检查
 * @param  params: 参数结构体指针
 * @retval AD9854_StatusTypeDef 检查结果
 */
AD9854_StatusTypeDef AD9854_ValidateParams(const AD9854_ControlParams_t *params)
{
    if (params == NULL) {
        return AD9854_PARAM_ERROR;
    }

    // 检查频率范围
    if (params->frequency_hz < AD9854_MIN_FREQUENCY ||
        params->frequency_hz > AD9854_MAX_FREQUENCY) {
        return AD9854_PARAM_ERROR;
    }

    // 检查目标峰峰值范围
    if (params->target_vpp_mv < MIN_TARGET_VPP ||
        params->target_vpp_mv > MAX_TARGET_VPP) {
        return AD9854_PARAM_ERROR;
    }

    // 检查增益系数范围
    if (params->gain_factor < MIN_GAIN_FACTOR ||
        params->gain_factor > MAX_GAIN_FACTOR) {
        return AD9854_PARAM_ERROR;
    }

    // 检查幅度控制码范围
    if (params->amplitude_code > AD9854_MAX_AMPLITUDE) {
        return AD9854_PARAM_ERROR;
    }

    return AD9854_OK;
}

/**
 * @brief  应用当前参数到硬件 (内部函数)
 * @param  None
 * @retval AD9854_StatusTypeDef 应用状态
 */
static AD9854_StatusTypeDef AD9854_ApplyCurrentParams(void)
{
    AD9854_StatusTypeDef status;

    // 参数有效性检查
    status = AD9854_ValidateParams(&g_control_params);
    if (status != AD9854_OK) {
        return status;
    }

    // 设置频率
    status = AD9854_SetFrequency(g_control_params.frequency_hz);
    if (status != AD9854_OK) {
        return status;
    }

    // 设置幅度 (如果输出使能)
    if (g_control_params.enable) {
        status = AD9854_SetAmplitude(g_control_params.amplitude_code);
    } else {
        status = AD9854_SetAmplitude(0);  // 禁用输出
    }

    return status;
}

/**
 * @brief  延时函数 (微秒级)
 * @param  us: 延时时间 (微秒)
 * @retval None
 */
void AD9854_Delay_us(uint32_t us)
{
    // 基于168MHz系统时钟的精确延时
    // 每个循环约6个时钟周期，168MHz下约35.7ns
    uint32_t cycles = us * 28;  // 约1微秒

    while (cycles--) {
        __NOP();  // 空操作指令
    }
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief  频率转换为48位频率字
 * @param  frequency: 频率值 (Hz)
 * @retval None
 */
static void AD9854_FrequencyToWord(double frequency)
{
    uint64_t freq_word_64;
    uint32_t freq_low, freq_high;
    
    // 计算48位频率字: FTW = (frequency * 2^48) / SYSCLK
    freq_word_64 = (uint64_t)(frequency * FREQ_CALC_CONSTANT / SYSCLK_DOUBLE);
    
    // 分离高32位和低32位
    freq_low = (uint32_t)(freq_word_64 & 0xFFFFFFFF);
    freq_high = (uint32_t)(freq_word_64 >> 32);
    
    // 转换为6字节数组 (小端序)
    freq_word[0] = (uint8_t)(freq_low & 0xFF);
    freq_word[1] = (uint8_t)((freq_low >> 8) & 0xFF);
    freq_word[2] = (uint8_t)((freq_low >> 16) & 0xFF);
    freq_word[3] = (uint8_t)((freq_low >> 24) & 0xFF);
    freq_word[4] = (uint8_t)(freq_high & 0xFF);
    freq_word[5] = (uint8_t)((freq_high >> 8) & 0xFF);
}

/**
 * @brief  写入48位频率字到寄存器
 * @param  reg_addr: 频率寄存器起始地址
 * @retval None
 */
static void AD9854_WriteFrequencyWord(uint8_t reg_addr)
{
    uint8_t i;
    
    // 写入6字节频率字 (从高字节到低字节)
    for (i = 0; i < 6; i++) {
        AD9854_WriteReg(reg_addr + i, freq_word[5 - i]);
    }
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
